extends Node

## 背包管理器 (重构版) - 薄管理器，专注于CRUD操作和信号发送
## 移除业务逻辑，使用ItemDatabase和TagUtil处理物品数据

# ================================
# 类型定义 (保持向后兼容)
# ================================

enum ItemType {
	CONSUMABLE,    # 消耗品
	EQUIPMENT,     # 装备
	MATERIAL,      # 材料
	KEY_ITEM,      # 关键物品
	MISC          # 杂项
}

enum EquipmentSlot {
	WEAPON,        # 武器
	ARMOR,         # 护甲
	ACCESSORY,     # 饰品
	SHOES,         # 鞋子
	HELMET        # 头盔
}

# ================================
# 物品模板类 (不可变的固有属性)
# ================================

class ItemTemplate:
	var id: String
	var name: String
	var description: String
	var icon_path: String
	var type: ItemType
	var stack_size: int = 1
	var value: int = 0
	var rarity: int = 0
	var tags: Dictionary = {}  # 不可变的固有属性标签

	func _init(item_id: String = "", item_name: String = "", item_desc: String = ""):
		id = item_id
		name = item_name
		description = item_desc

	func to_dict() -> Dictionary:
		return {
			"id": id,
			"name": name,
			"description": description,
			"icon_path": icon_path,
			"type": type,
			"stack_size": stack_size,
			"value": value,
			"rarity": rarity,
			"tags": tags
		}

	func from_dict(data: Dictionary):
		id = data.get("id", "")
		name = data.get("name", "")
		description = data.get("description", "")
		icon_path = data.get("icon_path", "")
		type = data.get("type", ItemType.MISC)
		stack_size = data.get("stack_size", 1)
		value = data.get("value", 0)
		rarity = data.get("rarity", 0)
		tags = data.get("tags", {})

# ================================
# 物品实例类 (可变的状态数据)
# ================================

class ItemInstance:
	var template: ItemTemplate  # 引用不可变模板
	var instance_data: Dictionary = {}  # 可变状态数据
	var created_time: float

	func _init(item_template: ItemTemplate):
		template = item_template
		created_time = Time.get_time_dict_from_system()["unix"]
		_initialize_instance_data()

	func _initialize_instance_data():
		# 从模板初始化可变状态
		if template and template.tags.has("use"):
			var base_uses = template.tags.get("use", 0)
			instance_data["remaining_uses"] = base_uses

	## 获取剩余使用次数
	func get_remaining_uses() -> int:
		return instance_data.get("remaining_uses", 0)

	## 消耗使用次数
	func consume_use():
		var current_uses = get_remaining_uses()
		if current_uses > 0:
			instance_data["remaining_uses"] = current_uses - 1
		# use == -1 (无限使用) 或 use == 0 (不可使用) 时不修改

	## 检查是否可使用
	func is_usable() -> bool:
		var use_value = get_remaining_uses()
		var template_use = template.tags.get("use", 0) if template else 0
		# 如果模板定义为无限使用(-1)，则始终可用
		if template_use == -1:
			return true
		return use_value > 0

	## 检查是否可装备
	func is_equipable() -> bool:
		if not template:
			return false
		var slot_value = template.tags.get("slot", "")
		return not slot_value.is_empty()

	func to_dict() -> Dictionary:
		return {
			"template_id": template.id if template else "",
			"instance_data": instance_data,
			"created_time": created_time
		}

	func from_dict(data: Dictionary, item_template: ItemTemplate):
		template = item_template
		instance_data = data.get("instance_data", {})
		created_time = data.get("created_time", Time.get_time_dict_from_system()["unix"])



class EquipmentTemplate extends ItemTemplate:
	var slot: EquipmentSlot
	var stats: Dictionary = {}
	var level_requirement: int = 1

	func _init(item_id: String = "", item_name: String = "", item_desc: String = ""):
		super(item_id, item_name, item_desc)
		type = ItemType.EQUIPMENT

	func to_dict() -> Dictionary:
		var base_dict = super.to_dict()
		base_dict["slot"] = slot
		base_dict["stats"] = stats
		base_dict["level_requirement"] = level_requirement
		return base_dict

	func from_dict(data: Dictionary):
		super.from_dict(data)
		slot = data.get("slot", EquipmentSlot.WEAPON)
		stats = data.get("stats", {})
		level_requirement = data.get("level_requirement", 1)



# ================================
# 背包槽位类 (重构版 - 管理实例数组)
# ================================

class InventorySlot:
	var item_instances: Array[ItemInstance] = []  # 存储物品实例数组
	var template_id: String = ""  # 模板ID，用于快速识别物品类型

	func _init():
		item_instances = []
		template_id = ""

	func is_empty() -> bool:
		return item_instances.is_empty()

	func get_quantity() -> int:
		return item_instances.size()

	func get_template() -> ItemTemplate:
		if not item_instances.is_empty():
			return item_instances[0].template
		return null

	func can_stack_with(template: ItemTemplate) -> bool:
		if is_empty():
			return true
		if template_id != template.id:
			return false
		return get_quantity() < template.stack_size

	## 添加物品实例
	func add_instances(instances: Array[ItemInstance]) -> Array[ItemInstance]:
		var remaining_instances: Array[ItemInstance] = []

		for instance in instances:
			if is_empty():
				# 空槽位，直接添加
				template_id = instance.template.id
				item_instances.append(instance)
			elif can_stack_with(instance.template):
				# 可以堆叠
				item_instances.append(instance)
			else:
				# 无法堆叠，返回剩余实例
				remaining_instances.append(instance)

		return remaining_instances

	## 移除指定数量的实例
	func remove_instances(amount: int) -> Array[ItemInstance]:
		var removed_instances: Array[ItemInstance] = []
		var to_remove = min(amount, item_instances.size())

		for i in range(to_remove):
			removed_instances.append(item_instances.pop_back())

		if item_instances.is_empty():
			template_id = ""

		return removed_instances

	## 获取第一个可用的实例（用于使用物品）
	func get_usable_instance() -> ItemInstance:
		for instance in item_instances:
			if instance.is_usable():
				return instance
		return null

	## 移除已用完的实例
	func cleanup_used_instances():
		var i = 0
		while i < item_instances.size():
			var instance = item_instances[i]
			# 如果实例已用完且不是无限使用，则移除
			if instance.get_remaining_uses() <= 0 and instance.template.tags.get("use", 0) != -1:
				item_instances.remove_at(i)
			else:
				i += 1

		if item_instances.is_empty():
			template_id = ""

	func to_dict() -> Dictionary:
		var instances_data = []
		for instance in item_instances:
			instances_data.append(instance.to_dict())

		return {
			"template_id": template_id,
			"instances": instances_data
		}

	func from_dict(data: Dictionary):
		template_id = data.get("template_id", "")
		var instances_data = data.get("instances", [])

		item_instances.clear()
		for instance_data in instances_data:
			var template = ItemDatabase.get_item(instance_data.get("template_id", ""))
			if template:
				var instance = ItemInstance.new(template)
				instance.from_dict(instance_data, template)
				item_instances.append(instance)



# ================================
# 常量
# ================================

const DEFAULT_INVENTORY_SIZE = 30
const MAX_INVENTORY_SIZE = 100

# ================================
# 核心数据
# ================================

var inventory_slots: Array[InventorySlot] = []
var equipped_items: Dictionary = {}
var inventory_size: int = DEFAULT_INVENTORY_SIZE

# ================================
# 信号定义 (扩展版)
# ================================

signal inventory_changed()
signal item_added(template: ItemTemplate, quantity: int)
signal item_removed(template: ItemTemplate, quantity: int)
signal item_used(template: ItemTemplate)  # 核心信号 - 被ItemEffectService监听
signal equipment_changed(slot: EquipmentSlot, old_equipment: EquipmentTemplate, new_equipment: EquipmentTemplate)  # 核心信号
signal inventory_slot_changed(slot_index: int)

# ================================
# 初始化 (简化版)
# ================================

func _ready():
	_logger("Inventory Manager (Refactored) Ready!")
	_initialize_inventory()
	# 不再初始化物品数据库 - 由ItemDatabase负责

func _initialize_inventory():
	inventory_slots.clear()
	for i in range(inventory_size):
		inventory_slots.append(InventorySlot.new())

	# 初始化装备槽位
	for slot in EquipmentSlot.values():
		equipped_items[slot] = null

	_logger("Inventory initialized with %d slots" % inventory_size)

## 清空背包 (用于测试)
func clear_inventory():
	for slot in inventory_slots:
		slot.item_instances.clear()
		slot.template_id = ""
	inventory_changed.emit()
	_logger("Inventory cleared")

# ================================
# 核心CRUD操作
# ================================

## 添加物品到背包
func add_item(item_id: String, quantity: int = 1) -> bool:
	var template = ItemDatabase.get_item(item_id)
	if not template:
		_logger("Item template not found in database: %s" % item_id)
		return false

	# 创建物品实例
	var instances: Array[ItemInstance] = []
	for i in range(quantity):
		var instance = ItemDatabase.create_item_instance(item_id)
		if instance:
			instances.append(instance)

	if instances.is_empty():
		_logger("Failed to create item instances for: %s" % item_id)
		return false

	var remaining_instances = instances

	# 先尝试堆叠到现有槽位
	for slot in inventory_slots:
		if not slot.is_empty() and slot.can_stack_with(template):
			remaining_instances = slot.add_instances(remaining_instances)
			if remaining_instances.is_empty():
				break

	# 如果还有剩余，寻找空槽位
	if not remaining_instances.is_empty():
		for slot in inventory_slots:
			if slot.is_empty():
				remaining_instances = slot.add_instances(remaining_instances)
				if remaining_instances.is_empty():
					break

	var added_quantity = quantity - remaining_instances.size()
	if added_quantity > 0:
		item_added.emit(template, added_quantity)
		inventory_changed.emit()
		_logger("Added %d x %s to inventory" % [added_quantity, template.name])

	if not remaining_instances.is_empty():
		_logger("Inventory full, could not add %d x %s" % [remaining_instances.size(), template.name])
		return false

	return true

## 移除物品 (按ID)
func remove_item(item_id: String, quantity: int = 1) -> int:
	var removed_total = 0
	var remaining = quantity
	var template_ref = null

	for slot in inventory_slots:
		if not slot.is_empty() and slot.template_id == item_id:
			if not template_ref:
				template_ref = slot.get_template()
			var removed_instances = slot.remove_instances(remaining)
			var removed_count = removed_instances.size()
			removed_total += removed_count
			remaining -= removed_count
			if remaining <= 0:
				break

	if removed_total > 0 and template_ref:
		item_removed.emit(template_ref, removed_total)
		inventory_changed.emit()
		_logger("Removed %d x %s from inventory" % [removed_total, item_id])

	return removed_total

## 移除物品 (按槽位)
func remove_item_from_slot(slot_index: int, quantity: int = 1) -> int:
	if slot_index < 0 or slot_index >= inventory_slots.size():
		_logger("Invalid slot index: %d" % slot_index)
		return 0

	var slot = inventory_slots[slot_index]
	if slot.is_empty():
		return 0

	var template = slot.get_template()
	var removed_instances = slot.remove_instances(quantity)
	var removed_count = removed_instances.size()

	if removed_count > 0:
		item_removed.emit(template, removed_count)
		inventory_changed.emit()
		inventory_slot_changed.emit(slot_index)
		_logger("Removed %d x %s from slot %d" % [removed_count, template.name, slot_index])

	return removed_count

## 使用物品 (重构版 - 使用实例系统)
func use_item(slot_index: int) -> bool:
	if slot_index < 0 or slot_index >= inventory_slots.size():
		return false

	var slot = inventory_slots[slot_index]
	if slot.is_empty():
		return false

	# 获取可用的实例
	var instance = slot.get_usable_instance()
	if not instance:
		_logger("No usable instance found in slot %d" % slot_index)
		return false

	var template = instance.template

	# 检查物品是否可使用
	if not TagUtil.is_usable(instance):
		_logger("Item cannot be used: %s" % template.name)
		return false

	# 发送使用信号 - 让ItemEffectService处理效果
	item_used.emit(template)  # 发送模板用于效果处理

	# 如果是消耗品，处理使用次数
	if template.type == ItemType.CONSUMABLE:
		var remaining_uses = instance.get_remaining_uses()
		var template_use = template.tags.get("use", 0)

		if template_use == -1:
			# 无限使用，不消耗
			pass
		elif remaining_uses > 0:
			# 消耗使用次数
			instance.consume_use()
			# 清理已用完的实例
			slot.cleanup_used_instances()
		else:
			# 使用次数为0，不应该能使用
			_logger("Warning: Item used but has no remaining uses: %s" % template.name)
			return false

		inventory_changed.emit()
		inventory_slot_changed.emit(slot_index)

	# 如果是装备，尝试装备
	elif template.type == ItemType.EQUIPMENT and TagUtil.is_equipable(instance):
		return equip_item(slot_index)

	return true

# ================================
# 装备系统 (简化版)
# ================================

## 装备物品
func equip_item(slot_index: int) -> bool:
	if slot_index < 0 or slot_index >= inventory_slots.size():
		return false

	var slot = inventory_slots[slot_index]
	if slot.is_empty():
		return false

	var template = slot.get_template()
	if template.type != ItemType.EQUIPMENT:
		return false

	var equipment_slot_type = TagUtil.get_equipment_slot(template)
	if equipment_slot_type == -1:
		_logger("Invalid equipment slot for item: %s" % template.name)
		return false

	# 卸下当前装备
	var old_equipment = equipped_items.get(equipment_slot_type)
	if old_equipment:
		if not add_item(old_equipment.id, 1):
			_logger("Cannot unequip: inventory full")
			return false

	# 装备新物品
	equipped_items[equipment_slot_type] = template
	var removed_instances = slot.remove_instances(1)

	# 发送装备变化信号 - 让ItemEffectService处理属性变化
	equipment_changed.emit(equipment_slot_type, old_equipment, template)
	inventory_changed.emit()
	inventory_slot_changed.emit(slot_index)

	_logger("Equipped %s in slot %s" % [template.name, EquipmentSlot.keys()[equipment_slot_type]])
	return true

## 卸下装备
func unequip_item(equipment_slot: EquipmentSlot) -> bool:
	var equipment = equipped_items.get(equipment_slot)
	if not equipment:
		return false

	# 检查背包空间
	if not add_item(equipment.id, 1):
		_logger("Cannot unequip: inventory full")
		return false

	var old_equipment = equipped_items[equipment_slot]
	equipped_items[equipment_slot] = null

	# 发送装备变化信号
	equipment_changed.emit(equipment_slot, old_equipment, null)

	_logger("Unequipped %s from slot %s" % [equipment.name, EquipmentSlot.keys()[equipment_slot]])
	return true

# ================================
# 查询接口
# ================================

func has_item(item_id: String, required_quantity: int = 1) -> bool:
	var total_quantity = get_item_quantity(item_id)
	return total_quantity >= required_quantity

func get_item_quantity(item_id: String) -> int:
	var total = 0
	for slot in inventory_slots:
		if not slot.is_empty() and slot.template_id == item_id:
			total += slot.get_quantity()
	return total

func get_inventory_slots() -> Array[InventorySlot]:
	return inventory_slots

func get_equipped_items() -> Dictionary:
	return equipped_items

func get_equipped_item(slot: EquipmentSlot) -> EquipmentTemplate:
	return equipped_items.get(slot)

func is_inventory_full() -> bool:
	for slot in inventory_slots:
		if slot.is_empty():
			return false
	return true

func get_empty_slot_count() -> int:
	var count = 0
	for slot in inventory_slots:
		if slot.is_empty():
			count += 1
	return count

# ================================
# 物品移动和整理 (暂时禁用 - 需要重构以支持实例系统)
# ================================

func move_item(from_slot: int, to_slot: int) -> bool:
	if from_slot < 0 or from_slot >= inventory_slots.size():
		_logger("Invalid from_slot: %d" % from_slot)
		return false
	if to_slot < 0 or to_slot >= inventory_slots.size():
		_logger("Invalid to_slot: %d" % to_slot)
		return false
	if from_slot == to_slot:
		return true
	
	var from_slot_obj = inventory_slots[from_slot]
	var to_slot_obj = inventory_slots[to_slot]
	
	if from_slot_obj.is_empty():
		_logger("Source slot is empty: %d" % from_slot)
		return false
	
	# 如果目标槽位为空，直接移动
	if to_slot_obj.is_empty():
		to_slot_obj.item = from_slot_obj.item
		to_slot_obj.quantity = from_slot_obj.quantity
		from_slot_obj.item = null
		from_slot_obj.quantity = 0
		
		inventory_changed.emit()
		inventory_slot_changed.emit(from_slot)
		inventory_slot_changed.emit(to_slot)
		_logger("Moved item from slot %d to slot %d" % [from_slot, to_slot])
		return true
	
	# 如果目标槽位有物品，尝试堆叠或交换
	if from_slot_obj.item.id == to_slot_obj.item.id and to_slot_obj.quantity < to_slot_obj.item.stack_size:
		# 尝试堆叠
		var space = to_slot_obj.item.stack_size - to_slot_obj.quantity
		var move_amount = min(from_slot_obj.quantity, space)
		
		to_slot_obj.quantity += move_amount
		from_slot_obj.quantity -= move_amount
		
		if from_slot_obj.quantity <= 0:
			from_slot_obj.item = null
			from_slot_obj.quantity = 0
		
		inventory_changed.emit()
		inventory_slot_changed.emit(from_slot)
		inventory_slot_changed.emit(to_slot)
		_logger("Stacked %d items from slot %d to slot %d" % [move_amount, from_slot, to_slot])
		return true
	else:
		# 交换物品
		return swap_items(from_slot, to_slot)

func swap_items(slot1: int, slot2: int) -> bool:
	if slot1 < 0 or slot1 >= inventory_slots.size():
		_logger("Invalid slot1: %d" % slot1)
		return false
	if slot2 < 0 or slot2 >= inventory_slots.size():
		_logger("Invalid slot2: %d" % slot2)
		return false
	if slot1 == slot2:
		return true
	
	var temp_item = inventory_slots[slot1].item
	var temp_quantity = inventory_slots[slot1].quantity
	
	inventory_slots[slot1].item = inventory_slots[slot2].item
	inventory_slots[slot1].quantity = inventory_slots[slot2].quantity
	
	inventory_slots[slot2].item = temp_item
	inventory_slots[slot2].quantity = temp_quantity
	
	inventory_changed.emit()
	inventory_slot_changed.emit(slot1)
	inventory_slot_changed.emit(slot2)
	_logger("Swapped items between slot %d and slot %d" % [slot1, slot2])
	return true

func organize_inventory():
	# 收集所有非空物品
	var items_to_sort: Array[Dictionary] = []
	
	for i in range(inventory_slots.size()):
		var slot = inventory_slots[i]
		if not slot.is_empty():
			items_to_sort.append({
				"item": slot.item,
				"quantity": slot.quantity,
				"original_slot": i
			})
	
	# 清空所有槽位
	for slot in inventory_slots:
		slot.item = null
		slot.quantity = 0
	
	# 按物品类型和ID排序
	items_to_sort.sort_custom(_compare_items_for_sorting)
	
	# 重新放置物品，优先堆叠
	var current_slot = 0
	
	for item_data in items_to_sort:
		var item = item_data.item
		var remaining_quantity = item_data.quantity
		
		# 首先尝试与已有的相同物品堆叠
		for i in range(current_slot):
			var slot = inventory_slots[i]
			if not slot.is_empty() and slot.item.id == item.id and slot.quantity < item.stack_size:
				var space = item.stack_size - slot.quantity
				var stack_amount = min(remaining_quantity, space)
				slot.quantity += stack_amount
				remaining_quantity -= stack_amount
				if remaining_quantity <= 0:
					break
		
		# 如果还有剩余，放到新槽位
		while remaining_quantity > 0 and current_slot < inventory_slots.size():
			var slot = inventory_slots[current_slot]
			var place_amount = min(remaining_quantity, item.stack_size)
			slot.item = item
			slot.quantity = place_amount
			remaining_quantity -= place_amount
			current_slot += 1
	
	inventory_changed.emit()
	_logger("Inventory organized")

func _compare_items_for_sorting(a: Dictionary, b: Dictionary) -> bool:
	var item_a = a.item as ItemTemplate
	var item_b = b.item as ItemTemplate
	
	# 首先按类型排序
	if item_a.type != item_b.type:
		return item_a.type < item_b.type
	
	# 然后按稀有度排序（高稀有度在前）
	if item_a.rarity != item_b.rarity:
		return item_a.rarity > item_b.rarity
	
	# 最后按物品ID排序
	return item_a.id < item_b.id

# ================================
# 存档系统集成 (保持不变)
# ================================

func get_inventory_data() -> Dictionary:
	var data = {
		"inventory_size": inventory_size,
		"slots": [],
		"equipped_items": {}
	}
	
	# 保存背包槽位
	for slot in inventory_slots:
		data.slots.append(slot.to_dict())
	
	# 保存装备
	for slot in equipped_items:
		if equipped_items[slot]:
			data.equipped_items[str(slot)] = equipped_items[slot].to_dict()
	
	return data

func set_inventory_data(data: Dictionary):
	if not data:
		return
	
	inventory_size = data.get("inventory_size", DEFAULT_INVENTORY_SIZE)
	
	# 恢复背包槽位
	inventory_slots.clear()
	var slots_data = data.get("slots", [])
	for i in range(inventory_size):
		var slot = InventorySlot.new()
		if i < slots_data.size():
			slot.from_dict(slots_data[i])
		inventory_slots.append(slot)
	
	# 恢复装备
	equipped_items.clear()
	for slot in EquipmentSlot.values():
		equipped_items[slot] = null
	
	var equipped_data = data.get("equipped_items", {})
	for slot_str in equipped_data:
		var slot_index = int(slot_str)
		var equipment_data = equipped_data[slot_str]
		var equipment = EquipmentTemplate.new()
		equipment.from_dict(equipment_data)
		equipped_items[slot_index] = equipment
	
	inventory_changed.emit()
	_logger("Inventory data loaded successfully")

# ================================
# 兼容性接口 (用于过渡期)
# ================================

## 获取物品数据库中的物品 (委托给ItemDatabase)
func get_item_from_database(item_id: String) -> ItemTemplate:
	return ItemDatabase.get_item(item_id)

# ================================
# 调试和工具函数
# ================================

func _logger(msg: String) -> void:
	print("[InventoryManager] %s" % msg)

func add_test_items():
	add_item("potion_health", 5)
	add_item("bread", 10)
	add_item("sword_iron", 1)
	add_item("armor_leather", 1)
	add_item("iron_ore", 20)
	_logger("Added test items to inventory")

func print_inventory():
	_logger("=== Inventory Contents ===")
	for i in range(inventory_slots.size()):
		var slot = inventory_slots[i]
		if not slot.is_empty():
			var template = slot.get_template()
			_logger("Slot %d: %s x%d" % [i, template.name, slot.get_quantity()])
			# 显示每个实例的状态
			for j in range(slot.item_instances.size()):
				var instance = slot.item_instances[j]
				_logger("  Instance %d: uses=%d" % [j, instance.get_remaining_uses()])

	_logger("=== Equipped Items ===")
	for slot in equipped_items:
		var equipment = equipped_items[slot]
		if equipment:
			_logger("%s: %s" % [EquipmentSlot.keys()[slot], equipment.name])

func debug_print_tags():
	_logger("=== Item Tags Debug ===")
	for i in range(inventory_slots.size()):
		var slot = inventory_slots[i]
		if not slot.is_empty():
			var template = slot.get_template()
			_logger("Slot %d: %s" % [i, template.name])
			if template.has("tags"):
				_logger("  Template Tags: %s" % template.tags)
			# 显示实例状态
			for j in range(slot.item_instances.size()):
				var instance = slot.item_instances[j]
				_logger("  Instance %d data: %s" % [j, instance.instance_data])
				_logger("  Usable: %s" % TagUtil.is_usable(instance))
				_logger("  Equipable: %s" % TagUtil.is_equipable(instance))
